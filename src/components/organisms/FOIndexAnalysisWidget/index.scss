.foIndexAnalysisWidget {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  border-radius: 12px;
  background-color: var(--surface-level-1);
  overflow: hidden;
}

.foIndexHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
  padding-bottom: 12px;
  padding-top: 0;
  padding-bottom: 12px;

  .headerLeft {
    color: var(--text-neutral-strong);
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 142.857% */
  }

  .headerRight {
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    display: flex;
    flex-direction: row;

    .tradeWith {
      color: var(--text-neutral-weak);
      margin-right: 8px;
    }

    .scalperText {
      color: var(--text-neutral-strong);
      margin-left: 4px;
    }
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: linear-gradient(180deg, #e7f1f8 0%, #ffffff 50.31%);
}

.headerDark {
  background: linear-gradient(180deg, #284052 0%, #202020 50.31%);
  --calls-oi-color: #b74040;
  --put-oi-color: #02a85d;
}

.headerLeft {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.headerRight {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.expiryButton {
  background-color: var(--background-notice-strong);
  padding: 2px 8px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    color: var(--text-universal-strong);
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }
}

.expiryDate {
  span {
    color: var(--text-universal-inverse-strong);
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }
}

.tabNavigation {
  display: flex;
  padding: 0 15px;
  border-bottom: 1px solid var(--border-neutral-variant);
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

.tab {
  background: none;
  border: none;
  padding: 10px 0;
  margin-right: 15px;
  font-size: 14px;
  color: var(--text-neutral-weak);
  white-space: nowrap;
  transition: all 0.2s ease;

  &.activeTab {
    color: var(--text-primary-strong);
    border-bottom: 2px solid var(--border-primary-strong);
    font-weight: 500;
  }
}

.chartContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  min-height: 140px;
}

.actionButtons {
  display: flex;
  gap: 12px;
  padding: 15px;
}

.buttonTextClassName {
  font-size: 14px !important;
  color: var(--text-primary-strong) !important;
  font-weight: 500;
}

.optionScalperText {
  color: var(--text-universal-strong) !important;
}

.cta {
  border: 1px solid var(--border-primary-strong) !important;
  padding: 10px;
  border-radius: 12px !important;
  background-color: transparent;
  text-align: center;

  font-size: 14px !important;
  color: var(--text-primary-strong);
  font-weight: 500;
  width: 50%;
}

.primaryFill {
  background-color: var(--background-primary-strong);
}

.indexSelector {
  margin-right: 10px;
}

.dropdownHeader {
  padding: 0px !important;
  border-radius: 40px;
  background: transparent;
  border: none !important;
  justify-content: normal !important;

  > div {
    font-size: 14px !important;
    color: var(--text-neutral-strong) !important;
    font-weight: 500;
  }
}

.dropdownList {
  width: 100%;
  padding: 0;
  text-align: center;
  background: var(--surface-level-1) !important;
  border: 1px solid var(--background-neutral-weak) !important;
  box-shadow: none !important;
  margin-top: 3px;
  width: 90px !important;
  padding: 0 !important;

  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  > div {
    color: var(--text-neutral-strong);
    font-size: 12px;
    margin: 0;
    padding: 10px;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--background-neutral-weak);
    text-align: start;

    &:last-child {
      border-bottom: none;
    }
  }
}

.downArrowStyle {
  margin-left: 4px;
}

.active {
  background: var(--background-neutral-weak);
  color: var(--text-primary-strong) !important;
  font-weight: 500;
}

.lightChart {
  min-height: 140px;
}
