.container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 44px;
  padding: 16px 16px 0;
  margin-bottom: 24px;
  box-sizing: border-box;
}

.putButton,
.callButton {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
  height: 44px;
  border-radius: 12px;
  border: none;
  width: 100%;
}

.putButton {
  background: var(--background-negative-strong);
}

.callButton {
  background: var(--background-positive-strong);
}

.putText,
.callText {
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  color: var(--text-universal-strong);
}
