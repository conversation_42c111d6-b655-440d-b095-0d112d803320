import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import styles from './index.scss';

const BuyOptions = ({ onBuyPut, onBuyCall }) => {
  const renderButton = ({
    text,
    buttonClass,
    textClass,
    iconName,
    onClick,
  }) => (
    <button type="button" className={buttonClass} onClick={onClick}>
      <span className={textClass}>{text}</span>
      <Icon name={iconName} width={16} height={16} />
    </button>
  );

  return (
    <div className={styles.container}>
      {renderButton({
        text: 'Buy Put',
        buttonClass: styles.putButton,
        textClass: styles.putText,
        iconName: ICONS_NAME.ARROW_RIGHT_DOWN,
        onClick: onBuyPut,
      })}
      {renderButton({
        text: 'Buy Call',
        buttonClass: styles.callButton,
        textClass: styles.callText,
        iconName: ICONS_NAME.ARROW_RIGHT_UP,
        onClick: onBuyCall,
      })}
    </div>
  );
};

export default BuyOptions;
