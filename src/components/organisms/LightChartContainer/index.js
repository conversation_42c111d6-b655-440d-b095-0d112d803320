import React, { useRef, memo, useMemo } from 'react';
import LightViewCharts from '../LightViewCharts';
import useLightViewCharts from '../LightViewCharts/useLightViewCharts';

const LightChartContainer = memo(
  ({
    symbol,
    range,
    isTransparent,
    chartConfig,
    showHighLow = true,
    height = null,
    show24hrChart = false,
    widgetRoute,
  }) => {
    const chartContainerRef = useRef();

    // Memoize the props object passed to useLightViewCharts
    const chartProps = useMemo(
      () => ({
        symbol,
        range,
        chartConfig,
      }),
      [symbol, range, chartConfig],
    ); // Only depend on essential values

    const { error, loading, chartData } = useLightViewCharts({
      ...chartProps,
      chartContainerRef,
      isTransparent,
      showHighLow,
      show24hrChart,
      widgetRoute,
    });

    // Memoize the props passed to LightViewCharts
    const lightViewProps = useMemo(
      () => ({
        symbol,
        error,
        loading,
        chartData,
        chartContainerRef,
        height,
      }),
      [symbol, error, loading, chartData, height],
    );

    return <LightViewCharts {...lightViewProps} />;
  },
  (prevProps, nextProps) =>
    // Custom comparison function for memo
    prevProps.symbol?.id === nextProps.symbol?.id &&
    prevProps.range === nextProps.range &&
    prevProps.chartConfig === nextProps.chartConfig,
);

export default LightChartContainer;
