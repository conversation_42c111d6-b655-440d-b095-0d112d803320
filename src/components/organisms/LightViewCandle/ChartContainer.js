import { memo } from 'react';
import DEFAULT_CHART_LIGHT from '../../../assets/icons/default-light-chart.png';
import DEFAULT_CHART_LIGHT_DARK from '../../../assets/icons/default-light-chart-dark.png';
import styles from './index.scss';
import { isDarkMode } from '../../../utils/commonUtil';

export const ChartContainer = memo((props) => {
  const {
    symbol = {},
    companyPageNavigation,
    error,
    loading,
    chartData,
    chartContainerRef,
    chartClickedEvent,
    allowClick = false,
    height,
  } = props;

  if (error || loading || (!loading && !chartData.length)) {
    return (
      <div className={styles.imgWrapper} style={height ? { height } : {}}>
        <img
          className={styles.img}
          src={isDarkMode() ? DEFAULT_CHART_LIGHT_DARK : DEFAULT_CHART_LIGHT}
          alt="fallbackChart"
          style={height ? { height, objectFit: 'contain' } : {}}
        />
      </div>
    );
  }

  return (
    <div
      onClick={() => {
        if (chartClickedEvent) chartClickedEvent();
        if (companyPageNavigation) companyPageNavigation(symbol);
      }}
      className={styles.chartContainer}
      style={height ? { height } : {}}
    >
      <div
        ref={chartContainerRef}
        className={styles.chartWrapper}
        style={!allowClick ? { pointerEvents: 'none', height } : { height }}
      />
    </div>
  );
});

export default ChartContainer;
